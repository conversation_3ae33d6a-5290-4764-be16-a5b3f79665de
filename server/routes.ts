import type { Express } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import { insertFolderSchema, insertChatSessionSchema, insertMessageSchema, updateChatSessionSchema } from "@shared/schema";
import { z } from "zod";
import { sendFeedbackEmail } from "./feedback";

const N8N_WEBHOOK_URL = process.env.N8N_WEBHOOK_URL || "https://n8n.sollution.ai/webhook/e98745cf-eea7-4ee3-86ba-54wezrdth";

// Helper function to send message to RA<PERSON><PERSON><PERSON>
async function sendToRAGFlow(message: string, sessionId: string, ragflowConfig: any, stream: boolean = false) {
  const { apiKey, baseUrl, chatId } = ragflowConfig;

  console.log(`[ragflow] ===== RAGFLOW REQUEST =====`);
  console.log(`[ragflow] Method: POST`);
  console.log(`[ragflow] URL: ${baseUrl}/api/v1/chats/${chatId}/completions`);
  console.log(`[ragflow] Headers:`, {
    "Content-Type": "application/json",
    "Authorization": `Bearer ${apiKey}`,
  });
  console.log(`[ragflow] Body:`, JSON.stringify({
    question: message,
    stream: stream,
    session_id: sessionId,
  }, null, 2));

  const response = await fetch(`${baseUrl}/api/v1/chats/${chatId}/completions`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${apiKey}`,
    },
    body: JSON.stringify({
      question: message,
      stream: stream,
      session_id: sessionId,
    }),
  });

  console.log(`[ragflow] ===== RAGFLOW RESPONSE =====`);
  console.log(`[ragflow] Status: ${response.status} ${response.statusText}`);
  console.log(`[ragflow] Headers:`, Object.fromEntries(response.headers.entries()));

  if (!response.ok) {
    const errorText = await response.text();
    console.error(`[ragflow] Error response body: ${errorText}`);
    throw new Error(`RAGFlow failed with status ${response.status}: ${errorText}`);
  }

  if (stream) {
    // Return the response object for streaming
    return response;
  } else {
    // Handle non-streaming response
    const responseText = await response.text();
    console.log(`[ragflow] Raw response:`, responseText);

    let ragflowData: any = {};
    if (responseText.trim()) {
      try {
        ragflowData = JSON.parse(responseText);
        console.log(`[ragflow] Parsed response:`, ragflowData);
      } catch (parseError) {
        console.log(`[ragflow] Non-JSON response, using as plain text`);
        ragflowData = { data: { answer: responseText } };
      }
    }

    return ragflowData;
  }
}

export async function registerRoutes(app: Express): Promise<Server> {
  // Configuration endpoint
  app.get("/api/config", (req, res) => {
    res.json({
      webhookUrl: N8N_WEBHOOK_URL,
      hasWebhookConfigured: !!process.env.N8N_WEBHOOK_URL,
    });
  });

  // Test webhook endpoint
  app.post("/api/test-webhook", async (req, res) => {
    try {
      const { webhookUrl, message } = req.body;
      const testUrl = webhookUrl || N8N_WEBHOOK_URL;
      const testMessage = message || "Hello, this is a test message from the chat application";

      console.log(`[webhook-test] ===== TEST REQUEST =====`);
      console.log(`[webhook-test] Method: POST`);
      console.log(`[webhook-test] URL: ${testUrl}`);
      console.log(`[webhook-test] Body:`, JSON.stringify({
        sessionId: "test-session",
        message: testMessage,
      }, null, 2));

      const testResponse = await fetch(testUrl, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "User-Agent": "ChatApp/1.0",
          "Accept": "application/json, text/plain, */*",
        },
        body: JSON.stringify({
          sessionId: "test-session",
          message: testMessage,
        }),
      });

      console.log(`[webhook-test] ===== TEST RESPONSE =====`);
      console.log(`[webhook-test] Status: ${testResponse.status} ${testResponse.statusText}`);
      console.log(`[webhook-test] Headers:`, Object.fromEntries(testResponse.headers.entries()));

      if (!testResponse.ok) {
        const errorText = await testResponse.text();
        console.error(`[webhook-test] Error: ${errorText}`);
        return res.status(400).json({
          success: false,
          error: `Webhook test failed: ${testResponse.status} - ${errorText}`
        });
      }

      const responseText = await testResponse.text();
      console.log(`[webhook-test] Raw response: ${responseText}`);

      let responseData: any = {};
      if (responseText.trim()) {
        try {
          responseData = JSON.parse(responseText);
        } catch (parseError) {
          responseData = { response: responseText };
        }
      }

      res.json({
        success: true,
        message: "Webhook test successful!",
        response: responseData,
        rawResponse: responseText
      });
    } catch (error) {
      console.error(`[webhook-test] Error:`, error);
      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : "Unknown error"
      });
    }
  });

  // Test RAGFlow endpoint
  app.post("/api/test-ragflow", async (req, res) => {
    try {
      const { apiKey, baseUrl, chatId } = req.body;
      const testMessage = "Hello, this is a test message from the chat application";

      console.log(`[ragflow-test] ===== TEST REQUEST =====`);
      console.log(`[ragflow-test] Method: POST`);
      console.log(`[ragflow-test] URL: ${baseUrl}/api/v1/chats/${chatId}/completions`);
      console.log(`[ragflow-test] Headers:`, {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${apiKey}`,
      });
      console.log(`[ragflow-test] Body:`, JSON.stringify({
        question: testMessage,
        stream: false,
      }, null, 2));

      // Use the correct RAGFlow API endpoint format
      const requestUrl = `${baseUrl}/api/v1/chats/${chatId}/completions`;
      const requestHeaders = {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${apiKey}`,
      };
      const requestBody = JSON.stringify({
        question: testMessage,
        stream: false,
      });

      console.log(`[ragflow-test] Final URL: ${requestUrl}`);
      console.log(`[ragflow-test] Final Headers:`, requestHeaders);
      console.log(`[ragflow-test] Final Body:`, requestBody);

      const testResponse = await fetch(requestUrl, {
        method: "POST",
        headers: requestHeaders,
        body: requestBody,
      });

      console.log(`[ragflow-test] ===== TEST RESPONSE =====`);
      console.log(`[ragflow-test] Status: ${testResponse.status} ${testResponse.statusText}`);
      console.log(`[ragflow-test] Headers:`, Object.fromEntries(testResponse.headers.entries()));

      const responseText = await testResponse.text();
      console.log(`[ragflow-test] Raw response: ${responseText}`);

      if (!testResponse.ok) {
        console.error(`[ragflow-test] Error response: ${responseText}`);

        // Try to parse error response as JSON, fallback to plain text
        let errorMessage = `RAGFlow test failed: ${testResponse.status} ${testResponse.statusText}`;
        try {
          const errorData = JSON.parse(responseText);
          if (errorData.message) {
            errorMessage = errorData.message;
          } else if (errorData.error) {
            errorMessage = errorData.error;
          } else if (errorData.detail) {
            errorMessage = errorData.detail;
          }
        } catch (parseError) {
          // If it's not JSON, include the raw response
          if (responseText.trim()) {
            errorMessage += ` - ${responseText}`;
          }
        }

        return res.status(400).json({
          success: false,
          error: errorMessage,
          rawResponse: responseText,
          statusCode: testResponse.status
        });
      }

      // Handle successful response
      let responseData: any = {};
      let isValidJson = false;

      if (responseText.trim()) {
        try {
          responseData = JSON.parse(responseText);
          isValidJson = true;
          console.log(`[ragflow-test] Parsed JSON response:`, responseData);
        } catch (parseError) {
          console.log(`[ragflow-test] Non-JSON response, treating as plain text`);
          responseData = {
            response: responseText,
            note: "Response was not valid JSON"
          };
        }
      } else {
        responseData = {
          response: "Empty response",
          note: "RAGFlow returned an empty response"
        };
      }

      res.json({
        success: true,
        message: "RAGFlow test successful!",
        response: responseData,
        rawResponse: responseText,
        isValidJson: isValidJson
      });
    } catch (error) {
      console.error(`[ragflow-test] Error:`, error);

      // Provide more specific error messages
      let errorMessage = "Unknown error occurred";
      if (error instanceof Error) {
        if (error.message.includes("fetch")) {
          errorMessage = "Failed to connect to RAGFlow server. Please check the base URL and network connectivity.";
        } else if (error.message.includes("JSON")) {
          errorMessage = "Invalid JSON response from RAGFlow server.";
        } else {
          errorMessage = error.message;
        }
      }

      res.status(500).json({
        success: false,
        error: errorMessage,
        originalError: error instanceof Error ? error.message : "Unknown error"
      });
    }
  });

  // RAGFlow streaming endpoint
  app.post("/api/ragflow-stream", async (req, res) => {
    try {
      const { message, sessionId, ragflowConfig } = req.body;

      if (!ragflowConfig || !ragflowConfig.apiKey || !ragflowConfig.baseUrl || !ragflowConfig.chatId) {
        return res.status(400).json({
          success: false,
          error: "Missing RAGFlow configuration"
        });
      }

      console.log(`[ragflow-stream] Starting streaming request for session: ${sessionId}`);

      // Set up Server-Sent Events
      res.writeHead(200, {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Cache-Control'
      });

      const ragflowResponse = await sendToRAGFlow(message, sessionId, ragflowConfig, true);

      if (ragflowResponse.body) {
        const reader = ragflowResponse.body.getReader();
        const decoder = new TextDecoder();

        try {
          while (true) {
            const { done, value } = await reader.read();

            if (done) {
              console.log(`[ragflow-stream] Stream completed for session: ${sessionId}`);
              res.write('data: [DONE]\n\n');
              break;
            }

            const chunk = decoder.decode(value, { stream: true });
            console.log(`[ragflow-stream] Received chunk:`, chunk);

            // Send chunk to client
            res.write(`data: ${JSON.stringify({ content: chunk })}\n\n`);
          }
        } catch (streamError) {
          console.error(`[ragflow-stream] Stream error:`, streamError);
          res.write(`data: ${JSON.stringify({ error: 'Stream error occurred' })}\n\n`);
        } finally {
          reader.releaseLock();
        }
      } else {
        console.error(`[ragflow-stream] No response body for streaming`);
        res.write(`data: ${JSON.stringify({ error: 'No stream available' })}\n\n`);
      }

      res.end();
    } catch (error) {
      console.error(`[ragflow-stream] Error:`, error);
      res.write(`data: ${JSON.stringify({ error: error instanceof Error ? error.message : 'Unknown error' })}\n\n`);
      res.end();
    }
  });

  // Feedback endpoint
  app.post("/api/feedback", async (req, res) => {
    try {
      const { message } = z.object({ message: z.string().min(1).max(500) }).parse(req.body);
      await sendFeedbackEmail(message);
      res.status(200).json({ success: true, message: "Feedback sent successfully!" });
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ success: false, error: "Invalid feedback data", details: error.errors });
      }
      console.error("Feedback submission error:", error);
      res.status(500).json({ success: false, error: "Failed to send feedback." });
    }
  });

  // Folders
  app.get("/api/folders", async (req, res) => {
    try {
      const folders = await storage.getFolders();
      res.json(folders);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch folders" });
    }
  });

  app.post("/api/folders", async (req, res) => {
    try {
      const folderData = insertFolderSchema.parse(req.body);
      const folder = await storage.createFolder(folderData);
      res.json(folder);
    } catch (error) {
      res.status(400).json({ message: "Invalid folder data" });
    }
  });

  app.put("/api/folders/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const folderData = insertFolderSchema.partial().parse(req.body);
      const folder = await storage.updateFolder(id, folderData);
      
      if (!folder) {
        return res.status(404).json({ message: "Folder not found" });
      }
      
      res.json(folder);
    } catch (error) {
      res.status(400).json({ message: "Invalid folder data" });
    }
  });

  app.delete("/api/folders/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const success = await storage.deleteFolder(id);
      
      if (!success) {
        return res.status(404).json({ message: "Folder not found" });
      }
      
      res.json({ success: true });
    } catch (error) {
      res.status(500).json({ message: "Failed to delete folder" });
    }
  });

  // Chat Sessions
  app.get("/api/chat-sessions", async (req, res) => {
    try {
      const sessions = await storage.getChatSessions();
      res.json(sessions);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch chat sessions" });
    }
  });

  app.post("/api/chat-sessions", async (req, res) => {
    try {
      const sessionData = insertChatSessionSchema.parse(req.body);
      const session = await storage.createChatSession(sessionData);
      res.json(session);
    } catch (error) {
      res.status(400).json({ message: "Invalid session data" });
    }
  });

  app.put("/api/chat-sessions/:id", async (req, res) => {
    try {
      const id = req.params.id;
      const sessionData = updateChatSessionSchema.parse(req.body);
      const session = await storage.updateChatSession(id, sessionData);
      
      if (!session) {
        return res.status(404).json({ message: "Chat session not found" });
      }
      
      res.json(session);
    } catch (error) {
      res.status(400).json({ message: "Invalid session data" });
    }
  });

  app.delete("/api/chat-sessions/:id", async (req, res) => {
    try {
      const id = req.params.id;
      const success = await storage.deleteChatSession(id);
      
      if (!success) {
        return res.status(404).json({ message: "Chat session not found" });
      }
      
      res.json({ success: true });
    } catch (error) {
      res.status(500).json({ message: "Failed to delete chat session" });
    }
  });

  app.post("/api/chat-sessions/:id/move", async (req, res) => {
    try {
      const sessionId = req.params.id;
      const { folderId } = z.object({ folderId: z.number().nullable() }).parse(req.body);
      
      const success = await storage.moveChatSessionToFolder(sessionId, folderId);
      
      if (!success) {
        return res.status(404).json({ message: "Chat session not found" });
      }
      
      res.json({ success: true });
    } catch (error) {
      res.status(400).json({ message: "Invalid move data" });
    }
  });

  // Messages
  app.get("/api/chat-sessions/:id/messages", async (req, res) => {
    try {
      const sessionId = req.params.id;
      const messages = await storage.getMessages(sessionId);
      res.json(messages);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch messages" });
    }
  });

  app.post("/api/chat-sessions/:id/messages", async (req, res) => {
    try {
      const sessionId = req.params.id;
      const messageData = insertMessageSchema.parse({
        ...req.body,
        sessionId,
      });
      
      // Create user message
      const userMessage = await storage.createMessage(messageData);
      
      // Get chatbot engine configuration from request headers or use default
      const chatbotEngine = req.headers['x-chatbot-engine'] as string || 'n8n';
      const ragflowConfig = req.headers['x-ragflow-config'] ? JSON.parse(req.headers['x-ragflow-config'] as string) : null;

      // Send to appropriate chatbot engine
      try {
        let aiResponseContent: string;

        if (chatbotEngine === 'ragflow' && ragflowConfig) {
          // Send to RAGFlow
          const ragflowData = await sendToRAGFlow(messageData.content, sessionId, ragflowConfig);

          // Handle RAGFlow response format: { code: 0, data: { answer: "...", reference: {}, session_id: "..." } }
          if (ragflowData.code === 0 && ragflowData.data && ragflowData.data.answer) {
            aiResponseContent = ragflowData.data.answer;
          } else if (ragflowData.data && ragflowData.data.answer) {
            aiResponseContent = ragflowData.data.answer;
          } else if (ragflowData.answer) {
            aiResponseContent = ragflowData.answer;
          } else {
            console.error(`[ragflow] Unexpected response format:`, ragflowData);
            aiResponseContent = `Hello! I see you said "${messageData.content}".

⚠️ **RAGFlow Response Issue**: RAGFlow responded but the answer format was unexpected.

Response received: ${JSON.stringify(ragflowData, null, 2)}

Please check your RAGFlow configuration and try again.`;
          }
        } else {
          // Send to n8n webhook (default)
          console.log(`[webhook] ===== WEBHOOK REQUEST =====`);
          console.log(`[webhook] Method: POST`);
          console.log(`[webhook] URL: ${N8N_WEBHOOK_URL}`);
          console.log(`[webhook] Headers:`, {
            "Content-Type": "application/json",
            "User-Agent": "ChatApp/1.0",
            "Accept": "application/json, text/plain, */*"
          });
          console.log(`[webhook] Body:`, JSON.stringify({
            sessionId,
            message: messageData.content,
          }, null, 2));

          const webhookResponse = await fetch(N8N_WEBHOOK_URL, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              "User-Agent": "ChatApp/1.0",
              "Accept": "application/json, text/plain, */*",
            },
            body: JSON.stringify({
              sessionId,
              message: messageData.content,
            }),
          });

          console.log(`[webhook] ===== WEBHOOK RESPONSE =====`);
          console.log(`[webhook] Status: ${webhookResponse.status} ${webhookResponse.statusText}`);
          console.log(`[webhook] Headers:`, Object.fromEntries(webhookResponse.headers.entries()));

          if (!webhookResponse.ok) {
            const errorText = await webhookResponse.text();
            console.error(`[webhook] Error response body: ${errorText}`);
            throw new Error(`Webhook failed with status ${webhookResponse.status}: ${errorText}`);
          }

          let webhookData: any = {};
          const responseText = await webhookResponse.text();
          console.log(`[webhook] Raw response:`, responseText);

          if (responseText.trim()) {
            try {
              webhookData = JSON.parse(responseText);
              console.log(`[webhook] Parsed response:`, webhookData);
            } catch (parseError) {
              console.log(`[webhook] Non-JSON response, using as plain text`);
              webhookData = { response: responseText };
            }
          }

          if (webhookData.output) {
            // Handle n8n response with "output" field
            aiResponseContent = webhookData.output;
          } else if (webhookData.response || webhookData.message || responseText) {
            // Handle other response formats
            aiResponseContent = webhookData.response || webhookData.message || responseText;
          } else {
            aiResponseContent = `Hello! I see you said "${messageData.content}".

⚠️ **n8n Configuration Issue**: Your webhook is receiving requests but returning empty responses.

To fix this, check your n8n workflow:
1. Make sure the workflow is **Active** (green toggle)
2. Add a "Respond to Webhook" node at the end
3. Configure it to return: {"response": "Your AI response here"}

Once fixed, I'll provide proper AI responses!`;
          }
        }

        const aiMessage = await storage.createMessage({
          sessionId,
          content: aiResponseContent,
          role: "assistant",
        });

        res.json({ userMessage, aiMessage });
      } catch (chatbotError) {
        console.error(`[chatbot] Error:`, chatbotError);

        // Create fallback AI message with more specific error info
        const errorMessage = chatbotError instanceof Error
          ? `AI service error: ${chatbotError.message}`
          : "I'm having trouble connecting to the AI service right now. Please try again later.";

        const aiMessage = await storage.createMessage({
          sessionId,
          content: errorMessage,
          role: "assistant",
        });

        res.json({ userMessage, aiMessage });
      }
    } catch (error) {
      res.status(400).json({ message: "Invalid message data" });
    }
  });

  app.delete("/api/chat-sessions/:id/messages", async (req, res) => {
    try {
      const sessionId = req.params.id;
      const success = await storage.deleteMessages(sessionId);
      
      if (!success) {
        return res.status(404).json({ message: "Chat session not found" });
      }
      
      res.json({ success: true });
    } catch (error) {
      res.status(500).json({ message: "Failed to clear messages" });
    }
  });

  const httpServer = createServer(app);
  return httpServer;
}
