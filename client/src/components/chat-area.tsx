import { useState, useEffect, useRef } from "react";
import { <PERSON><PERSON>, Settings, Download, Fan, Calendar, Menu, Copy, ThumbsUp, ThumbsDown } from "lucide-react";
import { Button } from "@/components/ui/button";
import { MessageInput } from "@/components/message-input";
import { ChatbotConfig } from "@/components/webhook-config";
import { useChat } from "@/hooks/use-chat";
import { useToast } from "@/hooks/use-toast";
import { format, isAfter, isBefore, startOfDay, endOfDay } from "date-fns";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import { Input } from "@/components/ui/input";
import { useIsMobile } from "@/hooks/use-mobile";

interface ChatAreaProps {
  sessionId: string | null;
  startDate?: string;
  endDate?: string;
  showWebhookConfig?: boolean;
  onCloseWebhookConfig?: () => void;
  onMenuClick?: () => void;
}

export function ChatArea({ 
  sessionId, 
  startDate = "", 
  endDate = "", 
  showWebhookConfig = false, 
  onCloseWebhookConfig,
  onMenuClick,
}: ChatAreaProps) {
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const { messages, sendMessage, isLoading, clearMessages } = useChat(sessionId);
  const { toast } = useToast();
  const [typingIndicator, setTypingIndicator] = useState(false);
  const [feedback, setFeedback] = useState<{[key: string]: 'like' | 'dislike' | null}>({});
  const isMobile = useIsMobile();

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    if (isLoading) {
      setTypingIndicator(true);
    } else {
      setTypingIndicator(false);
    }
  }, [isLoading]);

  const handleSendMessage = async (content: string) => {
    if (!sessionId) return;
    await sendMessage(content);
  };

  const handleCopy = (content: string) => {
    navigator.clipboard.writeText(content);
    toast({
      title: "Copied to clipboard",
      description: "The message has been copied.",
    });
  };

  const handleFeedback = (messageId: string, newFeedback: 'like' | 'dislike') => {
    setFeedback(prev => {
      const currentFeedback = prev[messageId];
      if (currentFeedback === newFeedback) {
        // If clicking the same button again, remove feedback
        const newFeedbackState = { ...prev };
        delete newFeedbackState[messageId];
        return newFeedbackState;
      }
      return { ...prev, [messageId]: newFeedback };
    });
  };

  const handleClearMessages = async () => {
    if (!sessionId) return;
    if (confirm("Are you sure you want to clear all messages?")) {
      await clearMessages();
    }
  };

  const handleDownloadChat = () => {
    if (!sessionId || messages.length === 0) return;
    
    const chatContent = filteredMessages.map(message => {
      const timestamp = format(new Date(message.timestamp), "yyyy-MM-dd HH:mm:ss");
      const role = message.role === "user" ? "User" : "AI Assistant";
      return `[${timestamp}] ${role}: ${message.content}`;
    }).join('\n\n');

    const blob = new Blob([chatContent], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `chat-session-${sessionId}-${format(new Date(), "yyyy-MM-dd")}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // Filter messages based on date range
  const filteredMessages = messages.filter(message => {
    if (!startDate && !endDate) return true;
    
    const messageDate = new Date(message.timestamp);
    const start = startDate ? startOfDay(new Date(startDate)) : null;
    const end = endDate ? endOfDay(new Date(endDate)) : null;
    
    if (start && end) {
      return !isBefore(messageDate, start) && !isAfter(messageDate, end);
    } else if (start) {
      return !isBefore(messageDate, start);
    } else if (end) {
      return !isAfter(messageDate, end);
    }
    
    return true;
  });

  if (!sessionId) {
    return (
      <div className="flex-1 flex items-center justify-center chat-area-bg">
        <div className="text-center">
          <div className="text-4xl mb-6">🤖</div>
          <h2 className="text-xl font-bold mb-3 text-foreground">Hey, I'm Jurbot</h2>
          <p className="text-xl text-muted-foreground">Please create a new chat</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 flex flex-col chat-area-bg">
      {/* Header */}
      <div className="p-4 border-b border-border bg-background/95 backdrop-blur">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            {isMobile && (
              <Button onClick={onMenuClick} size="sm" variant="ghost">
                <Menu className="h-6 w-6" />
              </Button>
            )}
            <div className="w-8 h-8 rounded-full bg-primary flex items-center justify-center">
              <Bot className="h-4 w-4 text-primary-foreground" />
            </div>
            <div>
              <h2 className="text-lg font-semibold">AI Assistant</h2>
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <span>{messages.length} messages</span>
                {(startDate || endDate) && (
                  <>
                    <span>•</span>
                    <span>Filtered</span>
                  </>
                )}
              </div>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button
              onClick={handleDownloadChat}
              size="sm"
              variant="outline"
              disabled={messages.length === 0}
            >
              <Download className="h-4 w-4" />
            </Button>
            <Button
              onClick={handleClearMessages}
              size="sm"
              variant="outline"
            >
              <Fan className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {filteredMessages.map((message) => (
          <div
            key={message.id}
            className={`flex ${message.role === "user" ? "justify-end" : "justify-start"}`}
          >
            <div className="max-w-[70%] lg:max-w-[60%] xl:max-w-[50%]">
              <div
                className={`rounded-2xl p-4 shadow-lg break-words ${
                  message.role === "user"
                    ? "chat-user-bubble rounded-br-md"
                    : "chat-ai-bubble rounded-bl-md"
                }`}
              >
                {message.role === "assistant" ? (
                  <div className="prose prose-sm max-w-none dark:prose-invert">
                    <ReactMarkdown 
                      remarkPlugins={[remarkGfm]}
                      components={{
                        p: ({ children }) => <p className="mb-2 last:mb-0">{children}</p>,
                        ul: ({ children }) => <ul className="mb-2 last:mb-0 ml-4">{children}</ul>,
                        ol: ({ children }) => <ol className="mb-2 last:mb-0 ml-4">{children}</ol>,
                        li: ({ children }) => <li className="mb-1">{children}</li>,
                        code: ({ children }) => <code className="bg-black/20 dark:bg-white/20 px-1 py-0.5 rounded text-sm">{children}</code>,
                        pre: ({ children }) => <pre className="bg-black/20 dark:bg-white/20 p-2 rounded text-sm overflow-x-auto">{children}</pre>,
                        blockquote: ({ children }) => <blockquote className="border-l-2 border-black/30 dark:border-white/30 pl-4 italic">{children}</blockquote>,
                        a: ({ href, children }) => <a href={href} className="text-blue-600 hover:text-blue-800 dark:text-blue-300 dark:hover:text-blue-200 underline break-all" target="_blank" rel="noopener noreferrer">{children}</a>,
                        h1: ({ children }) => <h1 className="text-lg font-bold mb-2">{children}</h1>,
                        h2: ({ children }) => <h2 className="text-base font-bold mb-2">{children}</h2>,
                        h3: ({ children }) => <h3 className="text-sm font-bold mb-1">{children}</h3>,
                      }}
                    >
                      {message.content}
                    </ReactMarkdown>
                  </div>
                ) : (
                  <p className="whitespace-pre-wrap break-words">{message.content}</p>
                )}
              </div>
              <div
                className={`flex items-center gap-2 mt-2 text-xs text-muted-foreground ${
                  message.role === "user" ? "justify-end" : "justify-start"
                }`}
              >
                <span>{format(new Date(message.timestamp), "h:mm a")}</span>
                {message.role === 'assistant' && (
                  <div className="flex items-center gap-1">
                    <Button variant="ghost" size="icon" className="h-6 w-6" onClick={() => handleFeedback(String(message.id), 'dislike')}>
                      <ThumbsDown className={`h-4 w-4 ${feedback[message.id] === 'dislike' ? 'text-red-500' : ''}`} />
                    </Button>
                    <Button variant="ghost" size="icon" className="h-6 w-6" onClick={() => handleFeedback(String(message.id), 'like')}>
                      <ThumbsUp className={`h-4 w-4 ${feedback[message.id] === 'like' ? 'text-blue-500' : ''}`} />
                    </Button>
                    <Button variant="ghost" size="icon" className="h-6 w-6" onClick={() => handleCopy(message.content)}>
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                )}
              </div>
            </div>
          </div>
        ))}

        {typingIndicator && (
          <div className="flex justify-start">
            <div className="max-w-xs lg:max-w-md xl:max-w-lg">
              <div className="chat-ai-bubble rounded-2xl rounded-bl-md p-4 shadow-lg">
                <div className="flex items-center gap-2">
                  <div className="flex gap-1">
                    <div className="w-2 h-2 bg-current rounded-full animate-bounce opacity-50"></div>
                    <div className="w-2 h-2 bg-current rounded-full animate-bounce opacity-50" style={{ animationDelay: "0.1s" }}></div>
                    <div className="w-2 h-2 bg-current rounded-full animate-bounce opacity-50" style={{ animationDelay: "0.2s" }}></div>
                  </div>
                  <span className="text-xs opacity-60">AI is typing...</span>
                </div>
              </div>
            </div>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Message Input */}
      <MessageInput
        onSendMessage={handleSendMessage}
        isLoading={isLoading}
        sessionId={sessionId}
      />

      {showWebhookConfig && onCloseWebhookConfig && (
        <ChatbotConfig onClose={onCloseWebhookConfig} />
      )}
    </div>
  );
}
