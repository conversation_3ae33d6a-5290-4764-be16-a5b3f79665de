import { useState, useEffect } from "react";
import { Settings, TestTube, CheckCircle, AlertCircle, Bot, Zap } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/hooks/use-toast";
import { getChatbotConfig, saveChatbotConfig, validateConfig, type ChatbotEngine, type ChatbotConfiguration } from "@/lib/chatbot-config";

interface ChatbotConfigProps {
  onClose: () => void;
}

export function ChatbotConfig({ onClose }: ChatbotConfigProps) {
  const [config, setConfig] = useState<ChatbotConfiguration>(() => getChatbotConfig());
  const [testResult, setTestResult] = useState<{ success: boolean; message: string } | null>(null);
  const [isTestingConnection, setIsTestingConnection] = useState(false);
  const { toast } = useToast();

  // Load saved configuration on component mount
  useEffect(() => {
    const savedConfig = getChatbotConfig();
    setConfig(savedConfig);
  }, []);

  const handleTestConnection = async () => {
    // Validate configuration before testing
    const validation = validateConfig(config);
    if (!validation.isValid) {
      toast({
        title: "Configuration Error",
        description: validation.errors.join(", "),
        variant: "destructive",
      });
      return;
    }

    setIsTestingConnection(true);
    setTestResult(null);

    try {
      const endpoint = config.engine === "n8n" ? "/api/test-webhook" : "/api/test-ragflow";
      const body = config.engine === "n8n"
        ? { webhookUrl: config.n8n.webhookUrl }
        : {
            apiKey: config.ragflow.apiKey,
            baseUrl: config.ragflow.baseUrl,
            chatId: config.ragflow.chatId
          };

      const response = await fetch(endpoint, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(body),
      });

      const data = await response.json();

      setTestResult({
        success: data.success,
        message: data.success ? `${config.engine.toUpperCase()} connection test successful!` : data.error || "Connection test failed",
      });

      if (data.success) {
        toast({
          title: "Success",
          description: `${config.engine.toUpperCase()} is working correctly!`,
        });
      } else {
        toast({
          title: "Test Failed",
          description: data.error || "Connection test failed",
          variant: "destructive",
        });
      }
    } catch (error) {
      const errorMessage = `Failed to test ${config.engine} connection: ` + (error instanceof Error ? error.message : "Unknown error");
      setTestResult({
        success: false,
        message: errorMessage,
      });
      toast({
        title: "Test Failed",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsTestingConnection(false);
    }
  };

  const handleSaveConfiguration = () => {
    // Validate configuration before saving
    const validation = validateConfig(config);
    if (!validation.isValid) {
      toast({
        title: "Configuration Error",
        description: validation.errors.join(", "),
        variant: "destructive",
      });
      return;
    }

    try {
      saveChatbotConfig(config);
      toast({
        title: "Configuration Saved",
        description: `${config.engine.toUpperCase()} engine configuration has been saved successfully.`,
      });
      onClose();
    } catch (error) {
      toast({
        title: "Save Failed",
        description: "Failed to save configuration. Please try again.",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
      <Card className="w-full max-w-2xl">
        <CardHeader className="text-center">
          <CardTitle className="flex items-center justify-center gap-2">
            <Bot className="h-5 w-5" />
            Chatbot Engine Configuration
          </CardTitle>
          <CardDescription>
            Choose and configure your preferred AI chatbot engine
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Engine Selection */}
          <div className="space-y-3">
            <Label className="text-base font-medium">Select Chatbot Engine</Label>
            <Select
              value={config.engine}
              onValueChange={(value: ChatbotEngine) => setConfig(prev => ({ ...prev, engine: value }))}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Choose your chatbot engine" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="n8n">
                  <div className="flex items-center gap-2">
                    <Zap className="h-4 w-4" />
                    <span>n8n Workflow</span>
                  </div>
                </SelectItem>
                <SelectItem value="ragflow">
                  <div className="flex items-center gap-2">
                    <Bot className="h-4 w-4" />
                    <span>RAGFlow</span>
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Configuration Tabs */}
          <Tabs value={config.engine} className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="n8n" className="flex items-center gap-2">
                <Zap className="h-4 w-4" />
                n8n
              </TabsTrigger>
              <TabsTrigger value="ragflow" className="flex items-center gap-2">
                <Bot className="h-4 w-4" />
                RAGFlow
              </TabsTrigger>
            </TabsList>

            <TabsContent value="n8n" className="space-y-4 mt-4">
              <div className="space-y-2">
                <Label htmlFor="n8n-webhook-url">N8N Webhook URL</Label>
                <Input
                  id="n8n-webhook-url"
                  type="url"
                  placeholder="https://your-n8n-instance.com/webhook/your-webhook-id"
                  value={config.n8n.webhookUrl}
                  onChange={(e) => setConfig(prev => ({
                    ...prev,
                    n8n: { ...prev.n8n, webhookUrl: e.target.value }
                  }))}
                />
              </div>
              <div className="text-sm text-muted-foreground">
                <p className="font-medium mb-1">Instructions:</p>
                <ol className="list-decimal list-inside space-y-1 text-xs">
                  <li>Copy your n8n webhook URL</li>
                  <li>Paste it in the field above</li>
                  <li>Click "Test Connection" to verify</li>
                  <li>Save configuration to apply changes</li>
                </ol>
              </div>
            </TabsContent>

            <TabsContent value="ragflow" className="space-y-4 mt-4">
              <div className="grid grid-cols-1 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="ragflow-api-key">RAGFlow API Key</Label>
                  <Input
                    id="ragflow-api-key"
                    type="password"
                    placeholder="Enter your RAGFlow API key"
                    value={config.ragflow.apiKey}
                    onChange={(e) => setConfig(prev => ({
                      ...prev,
                      ragflow: { ...prev.ragflow, apiKey: e.target.value }
                    }))}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="ragflow-base-url">RAGFlow Base URL</Label>
                  <Input
                    id="ragflow-base-url"
                    type="url"
                    placeholder="http://193.30.121.199"
                    value={config.ragflow.baseUrl}
                    onChange={(e) => setConfig(prev => ({
                      ...prev,
                      ragflow: { ...prev.ragflow, baseUrl: e.target.value }
                    }))}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="ragflow-chat-id">Chat ID</Label>
                  <Input
                    id="ragflow-chat-id"
                    type="text"
                    placeholder="2291b3525cfc11f0990e5a471b8c9178"
                    value={config.ragflow.chatId}
                    onChange={(e) => setConfig(prev => ({
                      ...prev,
                      ragflow: { ...prev.ragflow, chatId: e.target.value }
                    }))}
                  />
                </div>
              </div>
              <div className="text-sm text-muted-foreground">
                <p className="font-medium mb-1">Instructions:</p>
                <ol className="list-decimal list-inside space-y-1 text-xs">
                  <li>Enter your RAGFlow API key</li>
                  <li>Verify the base URL is correct</li>
                  <li>Enter the shared chat ID from your RAGFlow instance</li>
                  <li>Test the connection to ensure it works</li>
                  <li>Save configuration to apply changes</li>
                </ol>
              </div>
            </TabsContent>
          </Tabs>

          {/* Test Result */}
          {testResult && (
            <Alert className={testResult.success ? "border-green-500" : "border-red-500"}>
              {testResult.success ? (
                <CheckCircle className="h-4 w-4 text-green-500" />
              ) : (
                <AlertCircle className="h-4 w-4 text-red-500" />
              )}
              <AlertDescription>{testResult.message}</AlertDescription>
            </Alert>
          )}

          {/* Action Buttons */}
          <div className="flex gap-2 pt-4">
            <Button
              onClick={handleTestConnection}
              disabled={isTestingConnection}
              variant="outline"
              className="flex-1"
            >
              <TestTube className="h-4 w-4 mr-2" />
              {isTestingConnection ? "Testing..." : "Test Connection"}
            </Button>
            <Button
              onClick={handleSaveConfiguration}
              className="flex-1"
            >
              <Settings className="h-4 w-4 mr-2" />
              Save Configuration
            </Button>
            <Button onClick={onClose} variant="ghost">
              Close
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}