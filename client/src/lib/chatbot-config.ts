export type ChatbotEngine = "n8n" | "ragflow";

export interface N8nConfig {
  webhookUrl: string;
}

export interface RAGFlowConfig {
  apiKey: string;
  baseUrl: string;
  chatId: string;
  authToken?: string; // For shared chat URLs
}

export interface ChatbotConfiguration {
  engine: ChatbotEngine;
  n8n: N8nConfig;
  ragflow: RAGFlowConfig;
}

// Default configuration
const DEFAULT_CONFIG: ChatbotConfiguration = {
  engine: "n8n",
  n8n: {
    webhookUrl: "",
  },
  ragflow: {
    apiKey: "",
    baseUrl: "http://193.30.121.199",
    chatId: "2291b3525cfc11f0990e5a471b8c9178",
    authToken: "NkZGI2ZGVhNWNmOTExZjA4MTllNWE0Nz",
  },
};

// Helper function to parse RAGFlow shared URL
export function parseRAGFlowSharedUrl(url: string): { baseUrl: string; chatId: string; authToken: string } | null {
  try {
    const urlObj = new URL(url);
    const sharedId = urlObj.searchParams.get('shared_id');
    const auth = urlObj.searchParams.get('auth');

    if (sharedId && auth) {
      return {
        baseUrl: `${urlObj.protocol}//${urlObj.host}`,
        chatId: sharedId,
        authToken: auth,
      };
    }
  } catch (error) {
    console.error("Error parsing RAGFlow shared URL:", error);
  }
  return null;
}

// Get current chatbot configuration from localStorage
export function getChatbotConfig(): ChatbotConfiguration {
  try {
    const engine = (localStorage.getItem("chatbot_engine") as ChatbotEngine) || DEFAULT_CONFIG.engine;
    const n8nWebhookUrl = localStorage.getItem("n8n_webhook_url") || DEFAULT_CONFIG.n8n.webhookUrl;
    const ragflowApiKey = localStorage.getItem("ragflow_api_key") || DEFAULT_CONFIG.ragflow.apiKey;
    const ragflowBaseUrl = localStorage.getItem("ragflow_base_url") || DEFAULT_CONFIG.ragflow.baseUrl;
    const ragflowChatId = localStorage.getItem("ragflow_chat_id") || DEFAULT_CONFIG.ragflow.chatId;
    const ragflowAuthToken = localStorage.getItem("ragflow_auth_token") || DEFAULT_CONFIG.ragflow.authToken;

    return {
      engine,
      n8n: {
        webhookUrl: n8nWebhookUrl,
      },
      ragflow: {
        apiKey: ragflowApiKey,
        baseUrl: ragflowBaseUrl,
        chatId: ragflowChatId,
        authToken: ragflowAuthToken,
      },
    };
  } catch (error) {
    console.error("Error loading chatbot configuration:", error);
    return DEFAULT_CONFIG;
  }
}

// Save chatbot configuration to localStorage
export function saveChatbotConfig(config: ChatbotConfiguration): void {
  try {
    localStorage.setItem("chatbot_engine", config.engine);
    localStorage.setItem("n8n_webhook_url", config.n8n.webhookUrl);
    localStorage.setItem("ragflow_api_key", config.ragflow.apiKey);
    localStorage.setItem("ragflow_base_url", config.ragflow.baseUrl);
    localStorage.setItem("ragflow_chat_id", config.ragflow.chatId);
    localStorage.setItem("ragflow_auth_token", config.ragflow.authToken || "");
  } catch (error) {
    console.error("Error saving chatbot configuration:", error);
    throw new Error("Failed to save configuration");
  }
}

// Validate configuration for the selected engine
export function validateConfig(config: ChatbotConfiguration): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (config.engine === "n8n") {
    if (!config.n8n.webhookUrl.trim()) {
      errors.push("N8N webhook URL is required");
    } else if (!isValidUrl(config.n8n.webhookUrl)) {
      errors.push("N8N webhook URL must be a valid URL");
    }
  } else if (config.engine === "ragflow") {
    if (!config.ragflow.apiKey.trim()) {
      errors.push("RAGFlow API key is required");
    }
    if (!config.ragflow.baseUrl.trim()) {
      errors.push("RAGFlow base URL is required");
    } else if (!isValidUrl(config.ragflow.baseUrl)) {
      errors.push("RAGFlow base URL must be a valid URL");
    }
    if (!config.ragflow.chatId.trim()) {
      errors.push("RAGFlow chat ID is required");
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

// Helper function to validate URLs
function isValidUrl(url: string): boolean {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

// Get configuration for API requests
export function getApiHeaders(config: ChatbotConfiguration): Record<string, string> {
  const headers: Record<string, string> = {
    "Content-Type": "application/json",
    "X-Chatbot-Engine": config.engine,
  };

  if (config.engine === "ragflow") {
    headers["X-RAGFlow-Config"] = JSON.stringify({
      apiKey: config.ragflow.apiKey,
      baseUrl: config.ragflow.baseUrl,
      chatId: config.ragflow.chatId,
    });
  }

  return headers;
}

// Check if the current configuration is complete and valid
export function isConfigurationReady(): boolean {
  const config = getChatbotConfig();
  const validation = validateConfig(config);
  return validation.isValid;
}
