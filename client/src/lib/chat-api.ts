import { apiRequest } from "./queryClient";
import { getChatbotConfig, getApiHeaders, validateConfig } from "./chatbot-config";
import type { InsertFolder, InsertChatSession, InsertMessage, UpdateChatSession, Folder, ChatSession, Message } from "@shared/schema";

// API base path for production
const API_BASE = import.meta.env.PROD ? "/jurbot-chat/api" : "/api";

export const chatApi = {
  // Folders
  getFolders: async (): Promise<Folder[]> => {
    const response = await apiRequest("GET", `${API_BASE}/folders`);
    return response.json();
  },

  createFolder: async (folder: InsertFolder): Promise<Folder> => {
    const response = await apiRequest("POST", `${API_BASE}/folders`, folder);
    return response.json();
  },

  updateFolder: async (id: number, folder: Partial<InsertFolder>): Promise<Folder> => {
    const response = await apiRequest("PUT", `${API_BASE}/folders/${id}`, folder);
    return response.json();
  },

  deleteFolder: async (id: number): Promise<{ success: boolean }> => {
    const response = await apiRequest("DELETE", `${API_BASE}/folders/${id}`);
    return response.json();
  },

  // Chat Sessions
  getChatSessions: async (): Promise<ChatSession[]> => {
    const response = await apiRequest("GET", `${API_BASE}/chat-sessions`);
    return response.json();
  },

  createChatSession: async (session: InsertChatSession): Promise<ChatSession> => {
    const response = await apiRequest("POST", `${API_BASE}/chat-sessions`, session);
    return response.json();
  },

  updateChatSession: async (id: string, session: UpdateChatSession): Promise<ChatSession> => {
    const response = await apiRequest("PUT", `${API_BASE}/chat-sessions/${id}`, session);
    return response.json();
  },

  deleteChatSession: async (id: string): Promise<{ success: boolean }> => {
    const response = await apiRequest("DELETE", `${API_BASE}/chat-sessions/${id}`);
    return response.json();
  },

  moveChatSession: async (sessionId: string, folderId: number | null): Promise<{ success: boolean }> => {
    const response = await apiRequest("POST", `${API_BASE}/chat-sessions/${sessionId}/move`, { folderId });
    return response.json();
  },

  // Messages
  getMessages: async (sessionId: string): Promise<Message[]> => {
    const response = await apiRequest("GET", `${API_BASE}/chat-sessions/${sessionId}/messages`);
    return response.json();
  },

  sendMessage: async (sessionId: string, content: string): Promise<{ userMessage: Message; aiMessage: Message }> => {
    // Get and validate chatbot configuration
    const config = getChatbotConfig();
    const validation = validateConfig(config);

    if (!validation.isValid) {
      throw new Error(`Configuration error: ${validation.errors.join(", ")}`);
    }

    // Get appropriate headers for the selected engine
    const headers = getApiHeaders(config);

    try {
      const response = await fetch(`${API_BASE}/chat-sessions/${sessionId}/messages`, {
        method: "POST",
        headers,
        body: JSON.stringify({
          content,
          role: "user",
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
      }

      return response.json();
    } catch (error) {
      console.error("Error sending message:", error);

      // Provide more specific error messages based on the engine
      if (config.engine === "ragflow") {
        throw new Error("Failed to send message to RAGFlow. Please check your RAGFlow configuration and try again.");
      } else {
        throw new Error("Failed to send message to n8n. Please check your webhook configuration and try again.");
      }
    }
  },

  clearMessages: async (sessionId: string): Promise<{ success: boolean }> => {
    const response = await apiRequest("DELETE", `${API_BASE}/chat-sessions/${sessionId}/messages`);
    return response.json();
  },
};
