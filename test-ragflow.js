// Simple test script to verify RAGFlow integration
const fetch = require('node-fetch');

async function testRAGFlow() {
  const config = {
    apiKey: "test-key", // This would be the actual API key
    baseUrl: "http://**************",
    chatId: "2291b3525cfc11f0990e5a471b8c9178"
  };

  const testMessage = "Hello, this is a test message";

  console.log('Testing RAGFlow integration...');
  console.log('Config:', config);
  console.log('Message:', testMessage);

  try {
    const response = await fetch(`${config.baseUrl}/api/v1/chats/${config.chatId}/completions`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${config.apiKey}`,
      },
      body: JSON.stringify({
        question: testMessage,
        stream: false,
      }),
    });

    console.log('Response status:', response.status);
    console.log('Response headers:', Object.fromEntries(response.headers.entries()));

    const responseText = await response.text();
    console.log('Response body:', responseText);

    if (response.ok) {
      try {
        const data = JSON.parse(responseText);
        console.log('Parsed response:', JSON.stringify(data, null, 2));
      } catch (parseError) {
        console.log('Could not parse as JSON, raw response:', responseText);
      }
    } else {
      console.error('Request failed with status:', response.status);
    }

  } catch (error) {
    console.error('Error testing RAGFlow:', error.message);
  }
}

testRAGFlow();
